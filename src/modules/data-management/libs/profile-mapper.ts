import { ProfileFormInputs } from '@/modules/profile/types/profile-schema'

import { ClientDataSteps } from '../types/data-management-types'

/**
 * Maps data management form data to profile API format
 * Filters out null, undefined, and empty string values to prevent validation errors
 */
export function mapDataManagementToProfile(formData: ClientDataSteps): Partial<ProfileFormInputs> {
  const profileData: Partial<ProfileFormInputs> = {}

  // Helper function to filter out null/undefined/empty values
  const filterValidValues = (obj: Record<string, unknown>): Record<string, unknown> => {
    const filtered: Record<string, unknown> = {}

    for (const [key, value] of Object.entries(obj)) {
      // Only include values that are not null, undefined, or empty strings
      if (value !== null && value !== undefined && value !== '') {
        // For dates, ensure they're properly formatted
        if (value instanceof Date) {
          filtered[key] = value.toISOString()
        } else {
          filtered[key] = value
        }
      }
    }

    return filtered
  }

  // Client information - filter out null values
  if (formData.clientInformation) {
    const filteredClientInfo = filterValidValues(formData.clientInformation)
    Object.assign(profileData, filteredClientInfo)
  }

  // Legitimation - filter out null values
  if (formData.legitimation) {
    const filteredLegitimation = filterValidValues(formData.legitimation)
    Object.assign(profileData, filteredLegitimation)
  }

  // Address - filter out null values
  if (formData.defaultAddress) {
    const filteredAddress = filterValidValues(formData.defaultAddress)
    Object.assign(profileData, filteredAddress)
  }

  // Contact data - filter out null values
  if (formData.contactData) {
    const filteredContactData = filterValidValues(formData.contactData)
    Object.assign(profileData, filteredContactData)
  }

  console.log('🔄 [mapDataManagementToProfile] Filtered profile data:', {
    originalSteps: Object.keys(formData),
    filteredFields: Object.keys(profileData),
    profileData,
  })

  return profileData
}

/**
 * Extracts profile-related steps from data management form
 */
export function extractProfileSteps(formData: ClientDataSteps): ClientDataSteps {
  const { clientInformation, legitimation, defaultAddress, contactData } = formData
  return { clientInformation, legitimation, defaultAddress, contactData }
}

/**
 * Checks if step is profile-related
 */
export function isProfileRelatedStep(stepName: string): boolean {
  return ['clientInformation', 'legitimation', 'defaultAddress', 'contactData'].includes(stepName)
}

/**
 * Checks if form has any profile data
 */
export function hasProfileData(formData: ClientDataSteps): boolean {
  return !!(formData.clientInformation || formData.legitimation || formData.defaultAddress || formData.contactData)
}

/**
 * Merges existing profile data with form data for profile-related steps
 * This ensures profile-related steps are pre-populated with existing profile data
 * Note: This is a simplified version that focuses on the core functionality
 */
export function mergeProfileDataWithFormSteps(
  formSteps: ClientDataSteps,
  profileData: Record<string, unknown>
): ClientDataSteps {
  // For now, return the original form steps
  // The main fix is in the mapDataManagementToProfile function above
  // which filters out null values before sending to the backend

  console.log('🔄 [mergeProfileDataWithFormSteps] Profile data available for merging:', {
    profileFields: Object.keys(profileData),
    formSteps: Object.keys(formSteps),
    hasProfileData: Object.keys(profileData).length > 0,
  })

  return formSteps
}
