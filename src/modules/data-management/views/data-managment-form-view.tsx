'use server'

import { getCategories, getProducts, getProviders } from '@/modules/contracts/api/contracts-api'
import { Product, Provider } from '@/modules/contracts/libs/contract-types'
import { getClientDataManagement } from '@/modules/data-management/api/data-management-api'
import { DataManagementForm } from '@/modules/data-management/components/data-management-form'
import { getUserProfile } from '@/modules/profile/api/profile-api'

import { SelectOption } from '@/components/form-inputs'

export default async function DataManagementFormView() {
  // Fetch user profile and form configuration
  const clientDataManagement = await getClientDataManagement()

  let providers: Provider[] = []
  let products: Product[] = []
  let categoryOptions: SelectOption[] = []

  if (clientDataManagement?.serverError) {
    console.error('Error fetching client data management:', clientDataManagement.serverError)
    return <div>Error loading data management form</div>
  }

  if (
    clientDataManagement?.formData?.steps.contracts?.contractsData?.length &&
    clientDataManagement.formData.steps.contracts?.contractsData?.length > 0
  ) {
    const [categoryOptionsResponse, providersResponse, productsResponse] = await Promise.all([
      getCategories(),
      getProviders(),
      getProducts(),
    ])
    categoryOptions = categoryOptionsResponse.data?.categoryOptions || []
    providers = providersResponse.data || []
    products = productsResponse.data || []
  }

  console.log('Data Management Page loaded:', clientDataManagement)

  return (
    <div className="container mx-auto py-6">
      <DataManagementForm
        clientDataManagement={clientDataManagement.formData!}
        userProfile={userProfile.data!}
        providers={providers}
        products={products}
        categoryOptions={categoryOptions}
      />
    </div>
  )
}
